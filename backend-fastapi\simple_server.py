#!/usr/bin/env python3
"""
Simple server to test the clinical endpoint
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Simple Clinical Test")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Simple server running", "timestamp": datetime.now().isoformat()}

@app.get("/test")
async def test():
    return {"status": "ok", "message": "Test endpoint working"}

@app.get("/api/clinical/data")
async def test_clinical_data(limit: int = 10):
    """Test clinical data endpoint"""
    try:
        logger.info(f"Testing clinical endpoint with limit={limit}")
        
        # Import here to avoid startup issues
        from app.services.clinical_service import ClinicalService
        from app.models import ClinicalDataQuery
        
        query = ClinicalDataQuery(limit=limit)
        result = ClinicalService.get_clinical_data(query)
        
        return {
            "success": True,
            "data": {
                "visits": result.visits,
                "totalCount": result.total_count,
                "page": result.page,
                "limit": result.limit
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error in clinical data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("Starting simple server on port 8001...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
