#!/usr/bin/env python3
"""
Test script to verify the clinical endpoint fix
"""

import sys
import asyncio
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our modules
from app.services.clinical_service import ClinicalService
from app.models import ClinicalDataQuery, ApiResponse
from datetime import datetime

# Create a simple test app
app = FastAPI(title="Clinical API Test")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Clinical API Test Server", "status": "running"}

@app.get("/api/clinical/data")
async def get_clinical_data_test(limit: int = 10):
    """Test the clinical data endpoint"""
    try:
        logger.info(f"Testing clinical data endpoint with limit={limit}")
        
        query = ClinicalDataQuery(limit=limit)
        result = ClinicalService.get_clinical_data(query)
        
        return ApiResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error in clinical data test: {e}")
        return ApiResponse(
            success=False,
            data={"error": str(e)},
            timestamp=datetime.now().isoformat()
        )

if __name__ == "__main__":
    print("Starting Clinical API Test Server...")
    uvicorn.run(
        "test_clinical_endpoint:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
